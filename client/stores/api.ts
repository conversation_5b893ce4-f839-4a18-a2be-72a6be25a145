import { defineStore } from 'pinia'

interface ApiState {
  healthStatus: 'healthy' | 'unhealthy' | 'checking' | 'unknown'
  lastHealthCheck: Date | null
  metrics: any
  loading: {
    health: boolean
    metrics: boolean
    tasks: boolean
    prescripts: boolean
  }
}

export const useApiStore = defineStore('api', {
  state: (): ApiState => ({
    healthStatus: 'unknown',
    lastHealthCheck: null,
    metrics: null,
    loading: {
      health: false,
      metrics: false,
      tasks: false,
      prescripts: false
    }
  }),

  getters: {
    isHealthy: (state) => state.healthStatus === 'healthy',
    healthStatusColor: (state) => {
      switch (state.healthStatus) {
        case 'healthy': return 'green'
        case 'unhealthy': return 'red'
        case 'checking': return 'yellow'
        default: return 'gray'
      }
    }
  },

  actions: {
    async checkHealth() {
      this.loading.health = true
      this.healthStatus = 'checking'
      
      try {
        const response = await fetch('http://localhost:8000/health', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        
        if (data.status === 'healthy') {
          this.healthStatus = 'healthy'
        } else {
          this.healthStatus = 'unhealthy'
        }
        
        this.lastHealthCheck = new Date()
        return { success: true, data: response }
      } catch (error) {
        console.error('Health check failed:', error)
        this.healthStatus = 'unhealthy'
        this.lastHealthCheck = new Date()
        return { success: false, error }
      } finally {
        this.loading.health = false
      }
    },

    async getMetrics() {
      this.loading.metrics = true
      const authStore = useAuthStore()

      try {
        const response = await fetch('http://localhost:8000/metrics', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...authStore.getAuthHeaders()
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        this.metrics = data.data
        return { success: true, data }
      } catch (error) {
        console.error('Failed to get metrics:', error)
        return { success: false, error }
      } finally {
        this.loading.metrics = false
      }
    },

    async executeTask(instruction: string, url: string, options: any = {}) {
      this.loading.tasks = true
      const authStore = useAuthStore()

      try {
        console.log('executeTask called with:', { instruction, url, options })
        
        // Validate required fields
        if (!instruction || instruction.trim() === '') {
          throw new Error('Instruction is required')
        }
        
        // Prepare default options first
        const defaultOptions = {
          timeout: 30000,
          headless: true,
          viewport: { width: 1280, height: 720 }
        }
        
        // Merge options but preserve instruction and url
        const mergedOptions = { ...defaultOptions, ...options }
        
        const requestBody = {
          instruction: instruction.trim(),
          url,
          ...mergedOptions
        }

        console.log('Sending execute request:', requestBody)
        console.log('Request body stringified:', JSON.stringify(requestBody))
        console.log('Auth headers:', authStore.getAuthHeaders())

        const response = await fetch('http://localhost:8000/execute', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...authStore.getAuthHeaders()
          },
          body: JSON.stringify(requestBody)
        })

        if (!response.ok) {
          const errorText = await response.text()
          console.error('Execute error response:', errorText)
          
          let errorData
          try {
            errorData = JSON.parse(errorText)
          } catch (e) {
            errorData = { error: errorText }
          }
          
          throw new Error(`HTTP ${response.status}: ${response.statusText}`, { cause: errorData })
        }

        const data = await response.json()
        console.log('Execute response data:', data)
        console.log('Full response structure:', JSON.stringify(data, null, 2))
        console.log('Task ID from response:', data.data?.taskId)
        console.log('Data object:', data.data)
        return { success: true, data }
      } catch (error) {
        console.error('Task execution failed:', error)
        return { success: false, error }
      } finally {
        this.loading.tasks = false
      }
    },

    async getPrescripts() {
      this.loading.prescripts = true
      const authStore = useAuthStore()

      try {
        const response = await fetch('http://localhost:8000/prescripts', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...authStore.getAuthHeaders()
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        return { success: true, data }
      } catch (error) {
        console.error('Failed to get prescripts:', error)
        return { success: false, error }
      } finally {
        this.loading.prescripts = false
      }
    },

    async createPrescript(prescript: any) {
      this.loading.prescripts = true
      const authStore = useAuthStore()
      
      try {
        const config = useRuntimeConfig()
        const response = await $fetch(`${config.public.apiBase}/prescripts`, {
          method: 'POST',
          headers: authStore.getAuthHeaders(),
          body: prescript
        })
        
        return { success: true, data: response }
      } catch (error) {
        console.error('Failed to create prescript:', error)
        return { success: false, error }
      } finally {
        this.loading.prescripts = false
      }
    },

    async getTaskStatus(taskId: string) {
      const authStore = useAuthStore()
      
      try {
        const response = await fetch(`http://localhost:8000/tasks/${taskId}/status`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...authStore.getAuthHeaders()
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        return { success: true, data }
      } catch (error) {
        console.error('Failed to get task status:', error)
        return { success: false, error }
      }
    },

    async getTaskResult(taskId: string) {
      const authStore = useAuthStore()
      
      try {
        const response = await fetch(`http://localhost:8000/tasks/${taskId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...authStore.getAuthHeaders()
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        return { success: true, data }
      } catch (error) {
        console.error('Failed to get task result:', error)
        return { success: false, error }
      }
    },

    async getAllTasks() {
      const authStore = useAuthStore()
      
      try {
        const response = await fetch(`http://localhost:8000/tasks`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...authStore.getAuthHeaders()
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        return { success: true, data }
      } catch (error) {
        console.error('Failed to get all tasks:', error)
        return { success: false, error }
      }
    }
  }
})
