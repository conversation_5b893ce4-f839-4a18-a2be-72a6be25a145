<template>
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Execute Tasks</h1>
      <p class="mt-1 text-sm text-gray-500">
        Send natural language instructions to automate browser tasks
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Task Execution Form -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">New Task</h3>
        </template>

        <!-- Quick Templates -->
        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Templates</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
            <UButton
              v-for="(template, key) in quickTasks"
              :key="key"
              variant="outline"
              size="xs"
              @click="applyTemplate(template)"
              class="text-left justify-start"
            >
              {{ key }}
            </UButton>
          </div>
        </div>

        <UForm :schema="schema" :state="taskState" @submit="executeTask" class="space-y-4">
          <UFormGroup label="URL" name="url" help="The website URL to automate">
            <UInput 
              v-model="taskState.url" 
              placeholder="https://example.com"
              icon="i-heroicons-globe-alt"
            />
          </UFormGroup>
          
          <UFormGroup label="Instruction" name="instruction" help="Describe what you want to do in natural language">
            <UTextarea 
              v-model="taskState.instruction" 
              placeholder="Take a screenshot of the homepage"
              :rows="4"
            />
          </UFormGroup>
          
          <!-- Advanced Options -->
          <UAccordion :items="[{ label: 'Advanced Options', slot: 'advanced' }]">
            <template #advanced>
              <div class="space-y-4 pt-4">
                <UFormGroup label="Timeout (ms)" name="timeout">
                  <UInput 
                    v-model.number="taskState.options.timeout" 
                    type="number"
                    placeholder="30000"
                  />
                </UFormGroup>
                
                <UFormGroup label="Viewport Width" name="viewportWidth">
                  <UInput 
                    v-model.number="taskState.options.viewport.width" 
                    type="number"
                    placeholder="1280"
                  />
                </UFormGroup>
                
                <UFormGroup label="Viewport Height" name="viewportHeight">
                  <UInput 
                    v-model.number="taskState.options.viewport.height" 
                    type="number"
                    placeholder="720"
                  />
                </UFormGroup>
                
                <UFormGroup label="Headless Mode" name="headless">
                  <UToggle v-model="taskState.options.headless" />
                </UFormGroup>
              </div>
            </template>
          </UAccordion>
          
          <UButton 
            type="submit" 
            block 
            :loading="apiStore.loading.tasks"
            :disabled="apiStore.loading.tasks"
            icon="i-heroicons-play"
          >
            Execute Task
          </UButton>
        </UForm>
        
        <!-- Quick Actions -->
        <template #footer>
          <div class="space-y-2">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Quick Actions</h4>
            <div class="flex flex-wrap gap-2">
              <UButton 
                size="xs" 
                variant="outline"
                @click="setQuickTask('screenshot')"
              >
                📸 Screenshot
              </UButton>
              <UButton 
                size="xs" 
                variant="outline"
                @click="setQuickTask('scroll')"
              >
                📜 Scroll & Capture
              </UButton>
              <UButton 
                size="xs" 
                variant="outline"
                @click="setQuickTask('form')"
              >
                📝 Fill Form
              </UButton>
              <UButton 
                size="xs" 
                variant="outline"
                @click="setQuickTask('click')"
              >
                👆 Click Element
              </UButton>
            </div>
          </div>
        </template>
      </UCard>

      <!-- Task History -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Recent Tasks</h3>
            <UButton 
              variant="ghost" 
              size="xs" 
              icon="i-heroicons-arrow-path"
              @click="refreshTaskHistory"
              :loading="apiStore.loading.tasks"
            >
              Refresh
            </UButton>
          </div>
        </template>
        
        <div v-if="taskHistory.length === 0" class="text-center py-8">
          <Icon name="i-heroicons-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500">No tasks executed yet</p>
          <p class="text-sm text-gray-400">Execute your first task to see it here</p>
        </div>
        
        <div v-else class="space-y-3">
          <div 
            v-for="task in taskHistory" 
            :key="task.id"
            class="border rounded-lg p-3 hover:bg-gray-50 transition-colors"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                  {{ task.instruction }}
                </p>
                <p class="text-xs text-gray-500 mt-1">
                  {{ task.url }}
                </p>
                <div class="flex items-center space-x-2 mt-2">
                  <UBadge 
                    :color="getStatusColor(task.status)" 
                    variant="subtle"
                    size="xs"
                  >
                    {{ task.status }}
                  </UBadge>
                  <span class="text-xs text-gray-400">
                    {{ formatTime(task.timestamp) }}
                  </span>
                </div>
              </div>
              
              <div class="flex space-x-2">
                <UButton 
                  v-if="task.status === 'completed' && task.result"
                  variant="outline" 
                  size="xs" 
                  icon="i-heroicons-eye"
                  @click="viewTaskResult(task)"
                >
                  View Result
                </UButton>
                
                <UButton 
                  variant="ghost" 
                  size="xs" 
                  icon="i-heroicons-arrow-path"
                  @click="retryTask(task)"
                  :loading="apiStore.loading.tasks"
                >
                  Retry
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Task Result Modal -->
    <TaskResult 
      v-model="showResultModal" 
      :task="selectedTask" 
      :loading="apiStore.loading.tasks"
      @retry="retryTask"
    />
  </div>
</template>

<script setup>
import { z } from 'zod'
import { onMounted } from 'vue'

definePageMeta({
  middleware: 'auth'
})

const apiStore = useApiStore()
const toast = useToast()

// Form schema
const schema = z.object({
  url: z.string().url('Please enter a valid URL'),
  instruction: z.string().min(5, 'Please provide a clear instruction'),
  options: z.object({
    timeout: z.number().min(1000).max(300000).optional(),
    viewport: z.object({
      width: z.number().min(320).max(3840).optional(),
      height: z.number().min(240).max(2160).optional()
    }).optional(),
    headless: z.boolean().optional()
  }).optional()
})

// Form state
const taskState = reactive({
  url: 'https://httpbin.org',
  instruction: 'Take a screenshot of the homepage',
  options: {
    timeout: 30000,
    viewport: {
      width: 1280,
      height: 720
    },
    headless: true
  }
})

// Task history
const taskHistory = ref([])

// Modal state
const showResultModal = ref(false)
const selectedTask = ref(null)

// Quick task templates
const quickTasks = {
  screenshot: {
    instruction: 'Take a screenshot of the homepage',
    url: 'https://httpbin.org'
  },
  scroll: {
    instruction: 'Scroll down to the bottom of the page and take a screenshot',
    url: 'https://httpbin.org'
  },
  form: {
    instruction: 'Fill out any forms on the page with test data',
    url: 'https://httpbin.org/forms/post'
  },
  click: {
    instruction: 'Click on the first button or link found on the page',
    url: 'https://httpbin.org'
  }
}

const executeTask = async (validatedData) => {
  console.log('Form submission - validatedData:', validatedData)
  console.log('Form submission - taskState:', taskState)
  
  // Use taskState directly since UForm validation might not pass data correctly
  const data = {
    instruction: taskState.instruction,
    url: taskState.url,
    options: taskState.options
  }
  
  console.log('Final data to send:', data)
  
  const result = await apiStore.executeTask(data.instruction, data.url, data.options)
  
  if (result.success) {
    // Add to history
    const newTask = {
      id: result.data.data.taskId,
      instruction: data.instruction,
      url: data.url,
      status: result.data.data.status,
      timestamp: new Date(),
      result: null,
      screenshots: []
    }
    
    taskHistory.value.unshift(newTask)

    toast.add({
      title: 'Task submitted successfully',
      description: `Task ID: ${result.data.data.taskId}`,
      color: 'green'
    })

    // Start polling for task status
    pollTaskStatus(result.data.data.taskId)
  } else {
    toast.add({
      title: 'Task execution failed',
      description: result.error?.message || result.error?.data?.message || 'Unknown error occurred',
      color: 'red'
    })
  }
}

// Polling function to check task status
const pollTaskStatus = (taskId) => {
  const pollInterval = setInterval(async () => {
    try {
      const statusResult = await apiStore.getTaskStatus(taskId)
      
      if (statusResult.success) {
        const taskIndex = taskHistory.value.findIndex(task => task.id === taskId)
        if (taskIndex !== -1) {
          taskHistory.value[taskIndex].status = statusResult.data.data.status
          
          // If task is completed, get the full result
          if (statusResult.data.data.status === 'completed' || statusResult.data.data.status === 'failed') {
            const resultData = await apiStore.getTaskResult(taskId)
            if (resultData.success) {
              taskHistory.value[taskIndex].result = resultData.data.data
              taskHistory.value[taskIndex].screenshots = resultData.data.data.screenshots || []
            }
            
            // Stop polling
            clearInterval(pollInterval)
            
            // Show notification
            toast.add({
              title: statusResult.data.data.status === 'completed' ? 'Task completed!' : 'Task failed',
              description: `Task ${taskId} has ${statusResult.data.data.status}`,
              color: statusResult.data.data.status === 'completed' ? 'green' : 'red'
            })
          }
        }
      }
    } catch (error) {
      console.error('Error polling task status:', error)
      clearInterval(pollInterval)
    }
  }, 3000) // Poll every 3 seconds
  
  // Stop polling after 5 minutes
  setTimeout(() => {
    clearInterval(pollInterval)
  }, 300000)
}

const refreshTaskHistory = async () => {
  try {
    const result = await apiStore.getAllTasks()
    if (result.success) {
      taskHistory.value = result.data.data.map(task => ({
        id: task.taskId,
        instruction: task.instruction,
        url: task.url,
        status: task.status,
        timestamp: new Date(task.createdAt),
        result: task.result,
        screenshots: task.screenshots || []
      }))
    }
  } catch (error) {
    console.error('Error refreshing task history:', error)
  }
}

const viewTaskResult = (task) => {
  selectedTask.value = task
  showResultModal.value = true
}

const setQuickTask = (type) => {
  const template = quickTasks[type]
  if (template) {
    taskState.instruction = template.instruction
    taskState.url = template.url
  }
}

const applyTemplate = (template) => {
  taskState.instruction = template.instruction
  taskState.url = template.url
}

const retryTask = async (task) => {
  const result = await apiStore.executeTask(task.instruction, task.url, taskState.options)
  
  if (result.success) {
    const newTask = {
      id: result.data.data.taskId,
      instruction: task.instruction,
      url: task.url,
      status: result.data.data.status,
      timestamp: new Date(),
      result: null,
      screenshots: []
    }
    
    taskHistory.value.unshift(newTask)

    toast.add({
      title: 'Task retried successfully',
      description: `New Task ID: ${result.data.data.taskId}`,
      color: 'green'
    })

    // Start polling for task status
    pollTaskStatus(result.data.data.taskId)
  } else {
    toast.add({
      title: 'Task retry failed',
      description: result.error?.message || result.error?.data?.message || 'Unknown error occurred',
      color: 'red'
    })
  }
}

const getStatusColor = (status) => {
  switch (status) {
    case 'pending': return 'yellow'
    case 'queued': return 'yellow'
    case 'running': return 'blue'
    case 'completed': return 'green'
    case 'failed': return 'red'
    default: return 'gray'
  }
}

const formatTime = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

// Load task history on mount
onMounted(() => {
  refreshTaskHistory()
})
</script>
