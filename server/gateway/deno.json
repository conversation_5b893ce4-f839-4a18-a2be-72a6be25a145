{"lock": false, "tasks": {"start": "deno run -A --watch=src/,routes/,shared/ main.ts", "dev": "deno run -A --watch=src/,routes/,shared/,utils/,middleware/ main.ts", "dev:debug": "deno run -A --watch=src/,routes/,shared/ --inspect=0.0.0.0:9229 main.ts", "dev:simple": "deno run -A --watch main.ts", "build": "deno run -A build.ts", "preview": "deno run -A main.ts", "update": "deno run -A -r https://fresh.deno.dev/update .", "test": "deno test -A", "test:watch": "deno test -A --watch", "test:coverage": "deno test -A --coverage=coverage", "lint": "deno lint", "lint:fix": "deno lint --fix", "fmt": "deno fmt", "fmt:check": "deno fmt --check", "check": "deno fmt --check && deno lint && deno check **/*.ts && deno check **/*.tsx", "cache": "deno cache main.ts", "bundle": "deno run -A --unstable bundle main.ts dist/bundle.js", "compile": "deno compile -A --output dist/aery-gateway main.ts", "docker:build": "docker build -t aery-gateway .", "docker:run": "docker run -p 8000:8000 aery-gateway", "health": "curl -f http://localhost:8000/api/health || exit 1", "metrics": "curl -s http://localhost:8000/api/metrics", "clean": "rm -rf coverage/ dist/ .deno/"}, "lint": {"rules": {"tags": ["fresh", "recommended"]}}, "exclude": ["**/_fresh/*"], "imports": {"$fresh/": "https://deno.land/x/fresh@1.6.8/", "preact": "https://esm.sh/preact@10.19.6", "preact/": "https://esm.sh/preact@10.19.6/", "@preact/signals": "https://esm.sh/*@preact/signals@1.2.2", "@preact/signals-core": "https://esm.sh/*@preact/signals-core@1.5.1", "postgres": "https://deno.land/x/postgres@v0.17.0/mod.ts", "redis": "https://deno.land/x/redis@v0.32.3/mod.ts", "bcrypt": "https://deno.land/x/bcrypt@v0.4.1/mod.ts", "djwt": "https://deno.land/x/djwt@v3.0.1/mod.ts", "uuid": "https://deno.land/std@0.208.0/uuid/mod.ts", "crypto": "https://deno.land/std@0.208.0/crypto/mod.ts", "datetime": "https://deno.land/std@0.208.0/datetime/mod.ts", "path": "https://deno.land/std@0.208.0/path/mod.ts", "fs": "https://deno.land/std@0.208.0/fs/mod.ts", "http": "https://deno.land/std@0.208.0/http/mod.ts", "log": "https://deno.land/std@0.208.0/log/mod.ts", "testing": "https://deno.land/std@0.208.0/testing/mod.ts", "dotenv": "https://deno.land/std@0.208.0/dotenv/mod.ts", "zod": "https://deno.land/x/zod@v3.22.4/mod.ts", "@shared/": "./shared/"}, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "preact", "lib": ["deno.ns", "dom", "dom.asynciterable", "es2020"], "strict": true}, "fmt": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve", "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["**/_fresh/*", "**/node_modules/*"]}}