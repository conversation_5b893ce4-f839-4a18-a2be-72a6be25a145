// CORS Middleware for AERY API
export class CorsMiddleware {
  private static allowedOrigins = [
    "http://localhost:3000",
    "http://localhost:3001", 
    "http://localhost:8080",
    "https://aery.dev",
    "https://api.aery.dev"
  ];

  private static allowedMethods = [
    "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"
  ];

  private static allowedHeaders = [
    "Content-Type",
    "Authorization", 
    "X-API-Key",
    "X-Requested-With",
    "Accept",
    "Origin",
    "Cache-Control",
    "Pragma"
  ];

  /**
   * Aplica headers CORS a una respuesta
   */
  static applyCorsHeaders(request: Request, response: Response): Response {
    const origin = request.headers.get("Origin");
    const headers = new Headers(response.headers);

    // Verificar si el origin está permitido
    if (origin && this.allowedOrigins.includes(origin)) {
      headers.set("Access-Control-Allow-Origin", origin);
    } else if (!origin) {
      // Para requests sin origin (como Postman)
      headers.set("Access-Control-Allow-Origin", "*");
    }

    headers.set("Access-Control-Allow-Methods", this.allowedMethods.join(", "));
    headers.set("Access-Control-Allow-Headers", this.allowedHeaders.join(", "));
    headers.set("Access-Control-Allow-Credentials", "true");
    headers.set("Access-Control-Max-Age", "86400"); // 24 horas

    // Crear nueva respuesta con headers CORS
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers
    });
  }

  /**
   * Maneja requests OPTIONS (preflight)
   */
  static handlePreflight(request: Request): Response {
    const origin = request.headers.get("Origin");
    const headers = new Headers();

    if (origin && this.allowedOrigins.includes(origin)) {
      headers.set("Access-Control-Allow-Origin", origin);
    } else {
      headers.set("Access-Control-Allow-Origin", "*");
    }

    headers.set("Access-Control-Allow-Methods", this.allowedMethods.join(", "));
    headers.set("Access-Control-Allow-Headers", this.allowedHeaders.join(", "));
    headers.set("Access-Control-Allow-Credentials", "true");
    headers.set("Access-Control-Max-Age", "86400");

    return new Response(null, {
      status: 204,
      headers
    });
  }

  /**
   * Middleware wrapper para rutas
   */
  static wrap(handler: (req: Request, ctx: any) => Promise<Response> | Response) {
    return async (req: Request, ctx: any): Promise<Response> => {
      // Manejar preflight OPTIONS
      if (req.method === "OPTIONS") {
        return this.handlePreflight(req);
      }

      try {
        // Ejecutar el handler original
        const response = await handler(req, ctx);
        
        // Aplicar headers CORS
        return this.applyCorsHeaders(req, response);
      } catch (error) {
        console.error("Error in CORS middleware:", error);
        
        // Crear respuesta de error con CORS
        const errorResponse = new Response(
          JSON.stringify({
            error: "Internal Server Error",
            message: "An error occurred processing your request",
            timestamp: new Date().toISOString()
          }),
          {
            status: 500,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        return this.applyCorsHeaders(req, errorResponse);
      }
    };
  }
}
