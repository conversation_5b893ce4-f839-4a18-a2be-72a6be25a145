// AERY Health Check API - Simplified
import { HandlerContext } from "$fresh/server.ts";
import { CorsMiddleware } from "../src/middleware/cors.ts";

const healthHandler = (req: Request, _ctx: HandlerContext): Response => {
  const health = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    services: {
      api: { status: "healthy" },
      gateway: { status: "running" }
    }
  };

  return new Response(JSON.stringify(health, null, 2), {
    status: 200,
    headers: { "Content-Type": "application/json" }
  });
};

export const handler = CorsMiddleware.wrap(healthHandler);
