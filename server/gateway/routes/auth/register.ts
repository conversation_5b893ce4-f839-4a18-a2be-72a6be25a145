// AERY Register API
import { HandlerContext } from "$fresh/server.ts";
import { AuthService } from "../../src/lib/auth.ts";
import { db } from "../../src/lib/database.ts";
import { Utils } from "../../src/lib/utils.ts";
import type { User } from "../../../../shared/types/index.ts";
import { CorsMiddleware } from "../../src/middleware/cors.ts";

/**
 * Register endpoint
 */
const registerHandler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  if (req.method !== "POST") {
    return Utils.createErrorResponse(
      "Método no permitido",
      "METHOD_NOT_ALLOWED",
      405,
    );
  }

  try {
    const body = await req.json();

    // Validar entrada
    const validation = Utils.validateObject(body, ["email", "password"]);
    if (!validation.valid) {
      return Utils.createErrorResponse(
        "Campos requeridos faltantes",
        "MISSING_FIELDS",
        400,
        { missing: validation.missing },
      );
    }

    const { email, password, plan = "basic" } = body;

    // Validar formato de email
    if (!Utils.isValidEmail(email)) {
      return Utils.createErrorResponse(
        "Formato de email inválido",
        "INVALID_EMAIL_FORMAT",
        400,
      );
    }

    // Validar plan
    if (!Utils.isValidPlan(plan)) {
      return Utils.createErrorResponse(
        "Plan inválido",
        "INVALID_PLAN",
        400,
        { validPlans: ["basic", "premium", "enterprise"] },
      );
    }

    // Validar contraseña
    if (password.length < 8) {
      return Utils.createErrorResponse(
        "La contraseña debe tener al menos 8 caracteres",
        "PASSWORD_TOO_SHORT",
        400,
      );
    }

    // Verificar si el usuario ya existe
    const existingUser = await db.getUserByEmail(email);
    if (existingUser) {
      return Utils.createErrorResponse(
        "El email ya está registrado",
        "EMAIL_ALREADY_EXISTS",
        409,
      );
    }

    // Generar API Key (opcional)
    const apiKey = AuthService.generateApiKey();

    // Hash de contraseña (en un sistema real deberías usar bcrypt)
    // const passwordHash = await AuthService.hashPassword(password);

    // Crear usuario con contraseña
    const user = await db.createUserWithPassword(
      Utils.sanitizeInput(email),
      password, // En producción esto debería ser un hash
      plan,
      body.name // Opcional
    );

    // Generar JWT token
    const token = await AuthService.generateJwtToken(user);

    // Respuesta exitosa
    return Utils.createSuccessResponse(
      {
        user: {
          id: user.id,
          email: user.email,
          plan: user.plan,
          name: user.name,
          created_at: user.created_at,
        },
        token,
        expiresIn: "24h",
      },
      "Usuario registrado exitosamente",
      201,
    );
  } catch (error) {
    console.error("❌ Error en registro:", error);

    return Utils.createErrorResponse(
      "Error interno del servidor",
      "INTERNAL_SERVER_ERROR",
      500,
    );
  }
};

export const handler = CorsMiddleware.wrap(registerHandler);
