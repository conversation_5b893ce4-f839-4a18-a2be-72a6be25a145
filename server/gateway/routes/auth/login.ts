// AERY Login API
import { HandlerContext } from "$fresh/server.ts";
import { AuthService } from "../../src/lib/auth.ts";
import { db } from "../../src/lib/database.ts";
import { Utils, Validators } from "../../src/lib/utils.ts";
import { CorsMiddleware } from "../../src/middleware/cors.ts";

/**
 * Login endpoint
 */
const loginHandler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  if (req.method !== "POST") {
    return Utils.createErrorResponse(
      "Método no permitido",
      "METHOD_NOT_ALLOWED",
      405,
    );
  }

  try {
    const body = await req.json();

    // Validar entrada
    const validation = Utils.validateObject(body, ["email", "password"]);
    if (!validation.valid) {
      return Utils.createErrorResponse(
        "Campos requeridos faltantes",
        "MISSING_FIELDS",
        400,
        { missing: validation.missing },
      );
    }

    const { email, password } = body;

    // Validar formato de email
    if (!Utils.isValidEmail(email)) {
      return Utils.createErrorResponse(
        "Formato de email inválido",
        "INVALID_EMAIL_FORMAT",
        400,
      );
    }

    // Verificar usuario y contraseña
    const user = await db.verifyUserPassword(email, password);
    if (!user) {
      return Utils.createErrorResponse(
        "Credenciales inválidas",
        "INVALID_CREDENTIALS",
        401,
      );
    }

    // Verificar si el usuario está activo
    if (!user.is_active) {
      return Utils.createErrorResponse(
        "Cuenta desactivada",
        "ACCOUNT_DISABLED",
        401,
      );
    }

    // Generar JWT token
    const token = await AuthService.generateJwtToken(user);

    // Respuesta exitosa
    return Utils.createSuccessResponse({
      user: {
        id: user.id,
        email: user.email,
        plan: user.plan,
        name: user.name,
        created_at: user.created_at,
      },
      token,
      expiresIn: "24h",
    }, "Login exitoso");
  } catch (error) {
    console.error("❌ Error en login:", error);

    return Utils.createErrorResponse(
      "Error interno del servidor",
      "INTERNAL_SERVER_ERROR",
      500,
    );
  }
};

export const handler = CorsMiddleware.wrap(loginHandler);
