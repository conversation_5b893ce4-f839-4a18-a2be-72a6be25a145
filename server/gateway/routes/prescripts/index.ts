// AERY Prescripts API
import { HandlerContext } from "$fresh/server.ts";
import { AuthService } from "../../src/lib/auth.ts";
import { db } from "../../src/lib/database.ts";
import { Utils, Validators } from "../../src/lib/utils.ts";
import { CorsMiddleware } from "../../src/middleware/cors.ts";
import type { PreScript } from "../../../../shared/types/index.ts";

/**
 * Prescripts endpoint - GET and POST
 */
const prescriptsHandler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  // Autenticación
  const user = await authenticateRequest(req);
  if (!user) {
    return Utils.createErrorResponse(
      "No autorizado",
      "UNAUTHORIZED",
      401,
    );
  }

  if (req.method === "GET") {
    return handleGetPrescripts(req, user);
  } else if (req.method === "POST") {
    return handleCreatePrescript(req, user);
  } else {
    return Utils.createErrorResponse(
      "Método no permitido",
      "METHOD_NOT_ALLOWED",
      405,
    );
  }
};

/**
 * Obtener lista de pre-scripts
 */
async function handleGetPrescripts(req: Request, user: User): Promise<Response> {
  try {
    // Verificar permisos
    if (!AuthService.hasPermission(user, "access_prescripts")) {
      return Utils.createErrorResponse(
        "Sin permisos para acceder a pre-scripts",
        "INSUFFICIENT_PERMISSIONS",
        403,
      );
    }

    const url = new URL(req.url);
    const params = url.searchParams;

    // Parámetros de consulta
    const category = params.get("category");
    const search = params.get("search");
    const isActive = params.get("isActive");
    const limit = Math.min(parseInt(params.get("limit") || "50"), 100);
    const offset = Math.max(parseInt(params.get("offset") || "0"), 0);
    const sortBy = params.get("sortBy") || "createdAt";
    const sortOrder = params.get("sortOrder") || "desc";

    // Determinar si puede ver todos los pre-scripts
    const canViewAll = AuthService.hasPermission(user, "manage_prescripts");
    const userId = canViewAll ? undefined : user.id;

    // Construir filtros
    const filters: Record<string, unknown> = {
      userId,
      category,
      search,
      isActive: isActive ? isActive === "true" : undefined,
      limit,
      offset,
      sortBy,
      sortOrder,
    };

    // Obtener pre-scripts
    const [prescripts, totalCount] = await Promise.all([
      db.getPrescripts(filters),
      db.getPrescriptsCount(filters),
    ]);

    // Enriquecer con estadísticas de uso
    const enrichedPrescripts = await Promise.all(
      prescripts.map(async (prescript) => {
        const usageStats = await db.getPrescriptUsageStats(prescript.id);

        return {
          ...prescript,
          usageStats: {
            totalUses: usageStats.totalUses || 0,
            successRate: usageStats.successRate || 0,
            averageExecutionTime: usageStats.averageExecutionTime || 0,
            lastUsed: usageStats.lastUsed,
          },
        };
      }),
    );

    // Obtener categorías disponibles
    const categories = await db.getPrescriptCategories();

    const response = {
      prescripts: enrichedPrescripts,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
        page: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(totalCount / limit),
      },
      categories,
      filters,
    };

    return Utils.createSuccessResponse(response);
  } catch (error) {
    console.error("❌ Error obteniendo pre-scripts:", error);

    return Utils.createErrorResponse(
      "Error interno del servidor",
      "INTERNAL_SERVER_ERROR",
      500,
    );
  }
}

/**
 * Crear nuevo pre-script
 */
async function handleCreatePrescript(
  req: Request,
  user: User,
): Promise<Response> {
  try {
    // Verificar permisos
    if (!AuthService.hasPermission(user, "manage_prescripts")) {
      return Utils.createErrorResponse(
        "Sin permisos para crear pre-scripts",
        "INSUFFICIENT_PERMISSIONS",
        403,
      );
    }

    const body = await req.json();

    // Validar entrada
    const validation = Utils.validateObject(body, [
      "name",
      "description",
      "actions",
    ]);
    if (!validation.valid) {
      return Utils.createErrorResponse(
        "Campos requeridos faltantes",
        "MISSING_FIELDS",
        400,
        { missing: validation.missing },
      );
    }

    const {
      name,
      description,
      category = "general",
      actions,
      isPublic = false,
      tags = [],
      estimatedTime,
      metadata = {},
    } = body;

    // Validar configuración
    const configValidation = Validators.validatePrescriptConfig({
      name,
      description,
      actions,
    });

    if (!configValidation.valid) {
      return Utils.createErrorResponse(
        "Configuración de pre-script inválida",
        "INVALID_PRESCRIPT_CONFIG",
        400,
        { errors: configValidation.errors },
      );
    }

    // Validar acciones
    if (!Array.isArray(actions) || actions.length === 0) {
      return Utils.createErrorResponse(
        "Se requiere al menos una acción",
        "INVALID_ACTIONS",
        400,
      );
    }

    for (const action of actions) {
      if (!action.type || !Validators.isValidPlaywrightAction(action.type)) {
        return Utils.createErrorResponse(
          `Tipo de acción inválido: ${action.type}`,
          "INVALID_ACTION_TYPE",
          400,
        );
      }
    }

    // Verificar si ya existe un pre-script con el mismo nombre
    const existingPrescript = await db.getPrescriptByName(name, user.id);
    if (existingPrescript) {
      return Utils.createErrorResponse(
        "Ya existe un pre-script con ese nombre",
        "PRESCRIPT_NAME_EXISTS",
        409,
      );
    }

    // Crear pre-script
    const prescript: Omit<PreScript, "id" | "createdAt" | "updatedAt"> = {
      userId: user.id,
      name: Utils.sanitizeInput(name),
      description: Utils.sanitizeInput(description),
      category: Utils.sanitizeInput(category),
      actions,
      isActive: true,
      isPublic,
      tags: tags.map((tag: string) => Utils.sanitizeInput(tag)),
      estimatedTime: estimatedTime || calculateEstimatedTime(actions),
      metadata,
    };

    const prescriptId = await db.createPrescript(prescript);

    // Obtener pre-script creado
    const createdPrescript = await db.getPrescriptById(prescriptId);

    return Utils.createSuccessResponse(
      createdPrescript,
      "Pre-script creado exitosamente",
      201,
    );
  } catch (error) {
    console.error("❌ Error creando pre-script:", error);

    return Utils.createErrorResponse(
      "Error interno del servidor",
      "INTERNAL_SERVER_ERROR",
      500,
    );
  }
}

/**
 * Autenticar solicitud
 */
async function authenticateRequest(req: Request) {
  // Intentar autenticación por API Key
  const apiKey = req.headers.get("X-API-Key") ||
    req.headers.get("Authorization")?.replace("Bearer ", "");

  if (apiKey) {
    const user = await AuthService.authenticateApiKey(apiKey);
    if (user) return user;
  }

  // Intentar autenticación por JWT
  const authHeader = req.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.substring(7);
    const user = await AuthService.authenticateJwt(token);
    if (user) return user;
  }

  return null;
}

/**
 * Calcular tiempo estimado basado en acciones
 */
function calculateEstimatedTime(actions: PlaywrightAction[]): number {
  let totalTime = 0;

  for (const action of actions) {
    switch (action.type) {
      case "navigate":
        totalTime += 3000; // 3 segundos
        break;
      case "click":
        totalTime += 500; // 0.5 segundos
        break;
      case "type":
        totalTime += 1000 + (action.text?.length || 0) * 50; // Base + tiempo por carácter
        break;
      case "wait":
        totalTime += action.timeout || 1000;
        break;
      case "screenshot":
        totalTime += 1000; // 1 segundo
        break;
      case "scroll":
        totalTime += 500; // 0.5 segundos
        break;
      default:
        totalTime += 1000; // 1 segundo por defecto
    }
  }

  return totalTime;
}

export const handler = CorsMiddleware.wrap(prescriptsHandler);