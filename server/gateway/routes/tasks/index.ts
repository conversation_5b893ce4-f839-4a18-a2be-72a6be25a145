// AERY Tasks List API
import { HandlerContext } from "$fresh/server.ts";
import { AuthService } from "../../src/lib/auth.ts";
import { QueueService } from "../../src/lib/queue.ts";
import { db } from "../../src/lib/database.ts";
import { Utils, Validators } from "../../src/lib/utils.ts";
import { CorsMiddleware } from "../../src/middleware/cors.ts";

/**
 * Tasks list endpoint
 */
const tasksHandler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  if (req.method !== "GET") {
    return Utils.createErrorResponse(
      "Método no permitido",
      "METHOD_NOT_ALLOWED",
      405,
    );
  }

  try {
    // Autenticación
    const user = await authenticateRequest(req);
    if (!user) {
      return Utils.createErrorResponse(
        "No autorizado",
        "UNAUTHORIZED",
        401,
      );
    }

    const url = new URL(req.url);
    const params = url.searchParams;

    // Parámetros de consulta
    const status = params.get("status");
    const limit = Math.min(parseInt(params.get("limit") || "50"), 100);
    const offset = Math.max(parseInt(params.get("offset") || "0"), 0);
    const sortBy = params.get("sortBy") || "createdAt";
    const sortOrder = params.get("sortOrder") || "desc";
    const search = params.get("search");
    const startDate = params.get("startDate");
    const endDate = params.get("endDate");

    // Validar parámetros
    if (status && !Validators.isValidTaskStatus(status)) {
      return Utils.createErrorResponse(
        "Estado de tarea inválido",
        "INVALID_TASK_STATUS",
        400,
        {
          validStatuses: [
            "pending",
            "processing",
            "completed",
            "failed",
            "cancelled",
          ],
        },
      );
    }

    if (!["asc", "desc"].includes(sortOrder)) {
      return Utils.createErrorResponse(
        "Orden de clasificación inválido",
        "INVALID_SORT_ORDER",
        400,
        { validOrders: ["asc", "desc"] },
      );
    }

    // Determinar si el usuario puede ver todas las tareas
    const canViewAll = AuthService.hasPermission(user, "view_all_executions");
    const userId = canViewAll && params.get("userId")
      ? params.get("userId")
      : user.id;

    // Construir filtros
    const filters: Record<string, unknown> = {
      userId: userId,
      status,
      search,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      limit,
      offset,
      sortBy,
      sortOrder,
    };

    // Obtener tareas de la base de datos
    const [tasks, totalCount] = await Promise.all([
      db.getTaskExecutions(filters),
      db.getTaskExecutionsCount(filters),
    ]);

    // Enriquecer con información de la cola
    const enrichedTasks = await Promise.all(
      tasks.map(async (task) => {
        const queueTask = task.taskId
          ? await QueueService.getTaskStatus(task.taskId)
          : null;

        return {
          id: task.id,
          taskId: task.taskId,
          userId: task.userId,
          instruction: task.instruction,
          url: task.url,
          prescriptId: task.prescriptId,
          status: queueTask?.status || task.status,
          priority: task.priority,
          timeout: task.timeout,
          screenshots: task.screenshots,
          headless: task.headless,
          viewport: task.viewport,
          userAgent: task.userAgent,
          metadata: task.metadata,
          createdAt: task.createdAt,
          updatedAt: task.updatedAt,
          completedAt: queueTask?.completedAt || task.completedAt,
          startedAt: queueTask?.startedAt,
          workerId: queueTask?.workerId,
          retryCount: queueTask?.retryCount || 0,
          executionTime: queueTask?.result?.executionTime,
          success: queueTask?.result?.success,
          error: queueTask?.result?.error,
          // Solo incluir logs y screenshots si el usuario tiene permisos
          ...(canViewAll || task.userId === user.id
            ? {
              logs: queueTask?.result?.logs || [],
              screenshotUrls: queueTask?.result?.screenshots || [],
            }
            : {}),
        };
      }),
    );

    // Obtener estadísticas adicionales
    const stats = await getTaskStats(userId, canViewAll);

    // Respuesta
    const response = {
      tasks: enrichedTasks,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
        page: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(totalCount / limit),
      },
      stats,
      filters: {
        status,
        search,
        startDate,
        endDate,
        sortBy,
        sortOrder,
      },
    };

    return Utils.createSuccessResponse(response);
  } catch (error) {
    console.error("❌ Error obteniendo lista de tareas:", error);

    return Utils.createErrorResponse(
      "Error interno del servidor",
      "INTERNAL_SERVER_ERROR",
      500,
    );
  }
};

/**
 * Autenticar solicitud
 */
async function authenticateRequest(req: Request) {
  // Intentar autenticación por API Key
  const apiKey = req.headers.get("X-API-Key") ||
    req.headers.get("Authorization")?.replace("Bearer ", "");

  if (apiKey) {
    const user = await AuthService.authenticateApiKey(apiKey);
    if (user) return user;
  }

  // Intentar autenticación por JWT
  const authHeader = req.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.substring(7);
    const user = await AuthService.authenticateJwt(token);
    if (user) return user;
  }

  return null;
}

/**
 * Obtener estadísticas de tareas
 */
async function getTaskStats(
  userId: string,
  canViewAll: boolean,
): Promise<Record<string, number>> {
  try {
    const filters = canViewAll ? {} : { userId };

    const [
      totalTasks,
      pendingTasks,
      processingTasks,
      completedTasks,
      failedTasks,
    ] = await Promise.all([
      db.getTaskExecutionsCount(filters),
      db.getTaskExecutionsCount({ ...filters, status: "pending" }),
      db.getTaskExecutionsCount({ ...filters, status: "processing" }),
      db.getTaskExecutionsCount({ ...filters, status: "completed" }),
      db.getTaskExecutionsCount({ ...filters, status: "failed" }),
    ]);

    const successRate = totalTasks > 0
      ? (completedTasks / totalTasks) * 100
      : 0;

    return {
      total: totalTasks,
      pending: pendingTasks,
      processing: processingTasks,
      completed: completedTasks,
      failed: failedTasks,
      successRate: Math.round(successRate * 100) / 100,
    };
  } catch (error) {
    console.error("❌ Error obteniendo estadísticas:", error);
    return {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      successRate: 0,
    };
  }
}

export const handler = CorsMiddleware.wrap(tasksHandler);
