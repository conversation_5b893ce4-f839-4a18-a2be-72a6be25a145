// DO NOT EDIT. This file is generated by Fresh.
// This file SHOULD be checked into source version control.
// This file is automatically updated during development when running `dev.ts`.

import * as $api_auth_login from "./routes/auth/login.ts";
import * as $api_auth_register from "./routes/auth/register.ts";
import * as $api_execute from "./routes/execute.ts";
import * as $api_health from "./routes/health.ts";
import * as $api_metrics_index from "./routes/metrics/index.ts";
import * as $api_prescripts_index from "./routes/prescripts/index.ts";
import * as $api_tasks_index from "./routes/tasks/index.ts";

const manifest = {
  routes: {
    "./routes/auth/login.ts": $api_auth_login,
    "./routes/auth/register.ts": $api_auth_register,
    "./routes/execute.ts": $api_execute,
    "./routes/health.ts": $api_health,
    "./routes/metrics/index.ts": $api_metrics_index,
    "./routes/prescripts/index.ts": $api_prescripts_index,
    "./routes/tasks/index.ts": $api_tasks_index,
  },
  islands: {},
  baseUrl: import.meta.url,
};

export default manifest;
