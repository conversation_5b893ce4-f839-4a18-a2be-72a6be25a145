#!/usr/bin/env python3
"""
Coordinador de Agentes IA para AERY

Este módulo coordina múltiples agentes IA usando PocketFlow para
analizar instrucciones en lenguaje natural y generar acciones
de automatización web.
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from loguru import logger
from pocketflow import PocketFlow, Agent, Task, Workflow
from pocketflow.providers import OpenRouterProvider

from ..utils.config import get_config
from ..utils.prompts import PromptTemplates


class AgentCoordinator:
    """Coordinador principal de agentes IA"""
    
    def __init__(self):
        self.config = get_config()
        self.pocketflow: Optional[PocketFlow] = None
        self.agents: Dict[str, Agent] = {}
        self.workflows: Dict[str, Workflow] = {}
        self.prompt_templates = PromptTemplates()
        self.initialized = False
    
    async def initialize(self):
        """Inicializa PocketFlow y los agentes"""
        try:
            logger.info("🚀 Inicializando coordinador de agentes IA")
            
            # Configurar proveedor OpenRouter
            provider = OpenRouterProvider(
                api_key=self.config.openrouter_api_key,
                base_url="https://openrouter.ai/api/v1",
                default_model="anthropic/claude-3.5-sonnet"
            )
            
            # Inicializar PocketFlow
            self.pocketflow = PocketFlow(
                provider=provider,
                max_concurrent_tasks=3,
                retry_attempts=2,
                timeout=120
            )
            
            # Crear agentes especializados
            await self._create_agents()
            
            # Crear workflows
            await self._create_workflows()
            
            self.initialized = True
            logger.info("✅ Coordinador de agentes inicializado")
            
        except Exception as e:
            logger.error(f"❌ Error inicializando coordinador: {e}")
            raise
    
    async def _create_agents(self):
        """Crea los agentes especializados"""
        
        # Agente Analizador de Instrucciones
        self.agents["instruction_analyzer"] = Agent(
            name="InstructionAnalyzer",
            description="Analiza instrucciones en lenguaje natural y extrae intención, objetivos y contexto",
            system_prompt=self.prompt_templates.get_instruction_analyzer_prompt(),
            model="anthropic/claude-3.5-sonnet",
            temperature=0.1,
            max_tokens=2000
        )
        
        # Agente Planificador de Acciones
        self.agents["action_planner"] = Agent(
            name="ActionPlanner",
            description="Planifica secuencia de acciones web basada en análisis de instrucciones",
            system_prompt=self.prompt_templates.get_action_planner_prompt(),
            model="anthropic/claude-3.5-sonnet",
            temperature=0.2,
            max_tokens=3000
        )
        
        # Agente Selector de Elementos
        self.agents["element_selector"] = Agent(
            name="ElementSelector",
            description="Genera selectores CSS/XPath robustos para elementos web",
            system_prompt=self.prompt_templates.get_element_selector_prompt(),
            model="openai/gpt-4o",
            temperature=0.1,
            max_tokens=1500
        )
        
        # Agente Validador
        self.agents["validator"] = Agent(
            name="Validator",
            description="Valida y optimiza secuencias de acciones generadas",
            system_prompt=self.prompt_templates.get_validator_prompt(),
            model="anthropic/claude-3.5-sonnet",
            temperature=0.1,
            max_tokens=2000
        )
        
        # Agente de Self-Healing
        self.agents["self_healer"] = Agent(
            name="SelfHealer",
            description="Analiza errores y genera correcciones para acciones fallidas",
            system_prompt=self.prompt_templates.get_self_healer_prompt(),
            model="anthropic/claude-3.5-sonnet",
            temperature=0.3,
            max_tokens=2500
        )
        
        logger.info(f"✅ Creados {len(self.agents)} agentes especializados")
    
    async def _create_workflows(self):
        """Crea los workflows de procesamiento"""
        
        # Workflow secuencial (por defecto)
        self.workflows["sequential"] = Workflow(
            name="SequentialProcessing",
            description="Procesamiento secuencial de instrucciones",
            agents=[
                self.agents["instruction_analyzer"],
                self.agents["action_planner"],
                self.agents["element_selector"],
                self.agents["validator"]
            ],
            execution_mode="sequential"
        )
        
        # Workflow paralelo (para tareas complejas)
        self.workflows["parallel"] = Workflow(
            name="ParallelProcessing",
            description="Procesamiento paralelo para tareas complejas",
            agents=[
                self.agents["instruction_analyzer"],
                self.agents["action_planner"],
                self.agents["element_selector"]
            ],
            execution_mode="parallel",
            aggregator=self.agents["validator"]
        )
        
        logger.info(f"✅ Creados {len(self.workflows)} workflows")
    
    async def coordinate_agents(
        self,
        instruction: str,
        options: Dict[str, Any],
        strategy: str = "sequential"
    ) -> Dict[str, Any]:
        """Coordina agentes para procesar una instrucción"""
        
        if not self.initialized:
            raise RuntimeError("Coordinador no inicializado")
        
        start_time = time.time()
        
        try:
            logger.info(f"🧠 Coordinando agentes para: '{instruction[:100]}...'")
            
            # Preparar contexto
            context = {
                "instruction": instruction,
                "options": options,
                "timestamp": datetime.utcnow().isoformat(),
                "strategy": strategy
            }
            
            # Seleccionar workflow
            workflow = self.workflows.get(strategy, self.workflows["sequential"])
            
            # Ejecutar workflow
            result = await self._execute_workflow(workflow, context)
            
            execution_time = time.time() - start_time
            
            logger.info(f"✅ Coordinación completada en {execution_time:.2f}s")
            
            return {
                "success": True,
                "actions": result["actions"],
                "selectors": result.get("selectors", {}),
                "confidence": result.get("confidence", 0.0),
                "executionTime": execution_time,
                "strategy": strategy,
                "agentInsights": result.get("insights", {})
            }
            
        except Exception as e:
            logger.error(f"❌ Error coordinando agentes: {e}")
            return {
                "success": False,
                "error": str(e),
                "executionTime": time.time() - start_time,
                "strategy": strategy
            }
    
    async def _execute_workflow(
        self,
        workflow: Workflow,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Ejecuta un workflow específico"""
        
        try:
            # Crear tarea para el workflow
            task = Task(
                id=f"task_{int(time.time())}",
                input_data=context,
                workflow=workflow
            )
            
            # Ejecutar con PocketFlow
            result = await self.pocketflow.execute_task(task)
            
            # Procesar resultado
            return await self._process_workflow_result(result, context)
            
        except Exception as e:
            logger.error(f"❌ Error ejecutando workflow: {e}")
            raise
    
    async def _process_workflow_result(
        self,
        result: Any,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Procesa el resultado del workflow"""
        
        try:
            # Extraer datos de cada agente
            analysis = result.get("instruction_analyzer", {})
            plan = result.get("action_planner", {})
            selectors = result.get("element_selector", {})
            validation = result.get("validator", {})
            
            # Construir acciones finales
            actions = self._build_action_sequence(
                analysis, plan, selectors, validation
            )
            
            # Calcular confianza
            confidence = self._calculate_confidence(
                analysis, plan, selectors, validation
            )
            
            return {
                "actions": actions,
                "selectors": selectors,
                "confidence": confidence,
                "insights": {
                    "analysis": analysis,
                    "plan": plan,
                    "validation": validation
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Error procesando resultado: {e}")
            raise
    
    def _build_action_sequence(
        self,
        analysis: Dict,
        plan: Dict,
        selectors: Dict,
        validation: Dict
    ) -> List[Dict[str, Any]]:
        """Construye la secuencia final de acciones"""
        
        actions = []
        
        try:
            # Obtener acciones del plan
            planned_actions = plan.get("actions", [])
            
            for i, action in enumerate(planned_actions):
                # Enriquecer con selectores
                if action.get("target") and action["target"] in selectors:
                    action["selector"] = selectors[action["target"]]
                
                # Aplicar validaciones
                if validation.get("optimizations"):
                    for opt in validation["optimizations"]:
                        if opt.get("action_index") == i:
                            action.update(opt.get("changes", {}))
                
                # Añadir metadatos
                action["id"] = f"action_{i}"
                action["timestamp"] = datetime.utcnow().isoformat()
                
                actions.append(action)
            
            return actions
            
        except Exception as e:
            logger.error(f"❌ Error construyendo secuencia: {e}")
            return []
    
    def _calculate_confidence(
        self,
        analysis: Dict,
        plan: Dict,
        selectors: Dict,
        validation: Dict
    ) -> float:
        """Calcula la confianza general del resultado"""
        
        try:
            confidences = []
            
            # Confianza del análisis
            if analysis.get("confidence"):
                confidences.append(analysis["confidence"])
            
            # Confianza del plan
            if plan.get("confidence"):
                confidences.append(plan["confidence"])
            
            # Confianza de selectores
            if selectors.get("confidence"):
                confidences.append(selectors["confidence"])
            
            # Confianza de validación
            if validation.get("confidence"):
                confidences.append(validation["confidence"])
            
            # Promedio ponderado
            if confidences:
                return sum(confidences) / len(confidences)
            
            return 0.5  # Confianza neutral por defecto
            
        except Exception as e:
            logger.error(f"❌ Error calculando confianza: {e}")
            return 0.0
    
    async def analyze_and_heal(
        self,
        failed_actions: List[Dict[str, Any]],
        error: str,
        original_instruction: str
    ) -> Dict[str, Any]:
        """Analiza errores y genera correcciones"""
        
        try:
            logger.info(f"🔧 Analizando error para self-healing: {error[:100]}...")
            
            # Preparar contexto para self-healing
            healing_context = {
                "failed_actions": failed_actions,
                "error": error,
                "original_instruction": original_instruction,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Crear tarea para el agente de self-healing
            task = Task(
                id=f"healing_{int(time.time())}",
                input_data=healing_context,
                agent=self.agents["self_healer"]
            )
            
            # Ejecutar análisis
            result = await self.pocketflow.execute_task(task)
            
            if result.get("success"):
                logger.info("✅ Self-healing completado exitosamente")
                return {
                    "success": True,
                    "corrected_actions": result["corrected_actions"],
                    "reason": result.get("reason", "Error corregido"),
                    "confidence": result.get("confidence", 0.7)
                }
            else:
                logger.warning("⚠️ Self-healing no pudo corregir el error")
                return {
                    "success": False,
                    "reason": result.get("reason", "No se pudo corregir")
                }
                
        except Exception as e:
            logger.error(f"❌ Error en self-healing: {e}")
            return {
                "success": False,
                "reason": f"Error en análisis: {str(e)}"
            }
    
    async def generate_prescript(
        self,
        instruction: str,
        successful_actions: List[Dict[str, Any]],
        execution_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Genera un pre-script optimizado basado en ejecución exitosa"""
        
        try:
            logger.info(f"📝 Generando pre-script para: '{instruction[:50]}...'")
            
            # Preparar contexto
            context = {
                "instruction": instruction,
                "successful_actions": successful_actions,
                "execution_result": execution_result,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Usar agente planificador para optimizar
            task = Task(
                id=f"prescript_{int(time.time())}",
                input_data=context,
                agent=self.agents["action_planner"]
            )
            
            result = await self.pocketflow.execute_task(task)
            
            if result.get("success"):
                prescript = {
                    "version": "1.0",
                    "instruction_hash": context.get("instruction_hash"),
                    "actions": result["optimized_actions"],
                    "selectors": result.get("selectors", {}),
                    "metadata": {
                        "created_at": datetime.utcnow().isoformat(),
                        "success_rate": 1.0,
                        "avg_execution_time": execution_result.get("executionTime", 0),
                        "confidence": result.get("confidence", 0.8)
                    }
                }
                
                logger.info("✅ Pre-script generado exitosamente")
                return {
                    "success": True,
                    "prescript": prescript
                }
            else:
                logger.warning("⚠️ No se pudo generar pre-script")
                return {
                    "success": False,
                    "reason": "Optimización fallida"
                }
                
        except Exception as e:
            logger.error(f"❌ Error generando pre-script: {e}")
            return {
                "success": False,
                "reason": str(e)
            }
    
    async def get_agent_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas de los agentes"""
        
        try:
            stats = {
                "total_agents": len(self.agents),
                "total_workflows": len(self.workflows),
                "initialized": self.initialized,
                "agents": {}
            }
            
            for name, agent in self.agents.items():
                stats["agents"][name] = {
                    "name": agent.name,
                    "model": agent.model,
                    "temperature": agent.temperature,
                    "max_tokens": agent.max_tokens
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Error obteniendo estadísticas: {e}")
            return {}
    
    async def cleanup(self):
        """Limpia recursos del coordinador"""
        
        try:
            logger.info("🧹 Limpiando coordinador de agentes")
            
            if self.pocketflow:
                await self.pocketflow.cleanup()
            
            self.agents.clear()
            self.workflows.clear()
            self.initialized = False
            
            logger.info("✅ Coordinador limpiado")
            
        except Exception as e:
            logger.error(f"❌ Error limpiando coordinador: {e}")