# Dockerfile para Python Workers
FROM python:3.11-slim

# Variables de entorno
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    xvfb \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Crear usuario no-root
RUN groupadd -r worker && useradd -r -g worker worker

# Establecer directorio de trabajo
WORKDIR /app

# Copiar requirements y instalar dependencias de Python
COPY server/workers/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Instalar Playwright y navegadores
RUN playwright install chromium
RUN playwright install-deps chromium

# Copiar código fuente
COPY server/workers/ .

# Copiar archivos compartidos
COPY shared/ ./shared/

# Crear directorios necesarios
RUN mkdir -p /app/logs /app/artifacts /app/temp

# Cambiar permisos
RUN chown -R worker:worker /app

# Cambiar a usuario no-root
USER worker

# Variables de entorno por defecto
ENV AERY_WORKER_ID=worker-default
ENV AERY_REDIS_URL=redis://localhost:6379
ENV AERY_BROWSER_HEADLESS=true
ENV AERY_MAX_CONCURRENT_TASKS=3
ENV AERY_LOG_LEVEL=INFO
ENV AERY_ARTIFACTS_DIR=/app/artifacts
ENV AERY_TEMP_DIR=/app/temp
ENV PYTHONPATH=/app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD python -c "import redis; r = redis.from_url('$AERY_REDIS_URL'); r.ping()" || exit 1

# Comando por defecto
CMD ["python", "main.py"]