# AERY Environment Configuration

# Environment
ENVIRONMENT=development

# Server Configuration
SERVER_PORT=8000
SERVER_HOSTNAME=localhost
SERVER_BASE_URL=http://localhost:8000

# Database Configuration
DATABASE_URL=postgresql://aery:aery_dev_password@localhost:5432/aery
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=aery
DATABASE_USER=aery
DATABASE_PASSWORD=aery_dev_password
DATABASE_SSL=false
DATABASE_MAX_CONNECTIONS=20
DATABASE_CONNECTION_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000

# AI Services Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_DEFAULT_MODEL=anthropic/claude-3.5-sonnet
AI_TIMEOUT=60000
AI_MAX_RETRIES=3

# Security Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
API_SECRET_KEY=your_api_key_secret_here_also_make_it_random
ENCRYPTION_KEY=your_encryption_key_here_32_characters_long
SESSION_SECRET=your_session_secret_here

# Playwright Configuration
PLAYWRIGHT_TIMEOUT=30000
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_BROWSER=chromium
PLAYWRIGHT_VIEWPORT_WIDTH=1920
PLAYWRIGHT_VIEWPORT_HEIGHT=1080
PLAYWRIGHT_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
PLAYWRIGHT_MAX_CONCURRENT=5

# Pre-scripts Configuration
PRESCRIPTS_MAX_ACTIONS=50
PRESCRIPTS_TIMEOUT=300000
PRESCRIPTS_CACHE_TTL=3600
PRESCRIPTS_MAX_SIZE=1048576

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS_FREE=10
RATE_LIMIT_MAX_REQUESTS_PRO=100
RATE_LIMIT_MAX_REQUESTS_ENTERPRISE=1000

# Queue Configuration
QUEUE_DEFAULT_PRIORITY=5
QUEUE_MAX_RETRIES=3
QUEUE_RETRY_DELAY=5000
QUEUE_CLEANUP_INTERVAL=3600000
QUEUE_MAX_JOBS=10000

# Monitoring Configuration
METRICS_RETENTION_DAYS=30
METRICS_COLLECTION_INTERVAL=60000
LOG_LEVEL=info
LOG_FORMAT=json

# Worker Configuration
WORKER_HEARTBEAT_INTERVAL=30000
WORKER_TIMEOUT=300000
WORKER_MAX_MEMORY=512
WORKER_MAX_CPU=1

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp,text/plain,application/json

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8000
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-API-Key,X-Requested-With,Accept,Origin

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400000
BACKUP_RETENTION=7
BACKUP_S3_BUCKET=
BACKUP_S3_REGION=
BACKUP_S3_ACCESS_KEY=
BACKUP_S3_SECRET_KEY=

# Notification Configuration
NOTIFICATIONS_ENABLED=false
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# Webhook Configuration
WEBHOOKS_ENABLED=false
WEBHOOKS_SECRET=your_webhook_secret_here
WEBHOOKS_TIMEOUT=10000
WEBHOOKS_MAX_RETRIES=3

# Analytics Configuration
ANALYTICS_ENABLED=false
ANALYTICS_PROVIDER=
ANALYTICS_API_KEY=

# Feature Flags
FEATURE_MULTI_AGENT=true
FEATURE_AUTO_HEALING=true
FEATURE_ADVANCED_METRICS=true
FEATURE_WEBHOOKS=false
FEATURE_SSO=false
FEATURE_CUSTOM_DOMAINS=false