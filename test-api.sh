#!/usr/bin/env bash

# AERY API E2E Test Script
# Simple bash script to test the API endpoints

set -e

API_BASE="http://localhost:8000"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "FAIL")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "INFO")
            echo -e "${YELLOW}ℹ️  $message${NC}"
            ;;
    esac
}

# Function to test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local data=$4
    local headers=$5
    
    print_status "INFO" "Testing $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$API_BASE$endpoint" $headers)
    else
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X "$method" "$API_BASE$endpoint" \
                   -H "Content-Type: application/json" \
                   $headers \
                   -d "$data")
    fi
    
    # Extract status code
    status_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    response_body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status "OK" "$method $endpoint returned $status_code as expected"
        return 0
    else
        print_status "FAIL" "$method $endpoint returned $status_code, expected $expected_status"
        echo "Response: $response_body"
        return 1
    fi
}

# Function to wait for API to be ready
wait_for_api() {
    print_status "INFO" "Waiting for API to be ready..."
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$API_BASE/health" > /dev/null 2>&1; then
            print_status "OK" "API is ready!"
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts - API not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_status "FAIL" "API failed to start within timeout"
    return 1
}

# Main test execution
main() {
    echo "🚀 Starting AERY API E2E Tests"
    echo "================================"
    
    # Wait for API to be ready
    if ! wait_for_api; then
        exit 1
    fi
    
    # Test Variables
    TEST_EMAIL="test-$(date +%s)@example.com"
    TEST_PASSWORD="test-password-123"
    AUTH_TOKEN=""
    
    # Test 1: Health Check
    echo -e "\n📋 Test 1: Health Check"
    test_endpoint "GET" "/health" "200"
    
    # Test 2: User Registration
    echo -e "\n👤 Test 2: User Registration"
    registration_data="{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\",\"plan\":\"basic\"}"
    
    if response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/auth/register" \
                  -H "Content-Type: application/json" \
                  -d "$registration_data"); then
        
        status_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        response_body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
        
        if [ "$status_code" = "201" ] || [ "$status_code" = "200" ]; then
            print_status "OK" "User registration successful"
            # Extract token if available
            AUTH_TOKEN=$(echo "$response_body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "")
            if [ -n "$AUTH_TOKEN" ]; then
                print_status "OK" "Auth token extracted successfully"
            fi
        else
            print_status "FAIL" "User registration failed with status $status_code"
            echo "Response: $response_body"
        fi
    fi
    
    # Test 3: User Login
    echo -e "\n🔐 Test 3: User Login"
    login_data="{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}"
    
    if response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/auth/login" \
                  -H "Content-Type: application/json" \
                  -d "$login_data"); then
        
        status_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
        response_body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
        
        if [ "$status_code" = "200" ]; then
            print_status "OK" "User login successful"
            # Update token from login
            NEW_TOKEN=$(echo "$response_body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4 2>/dev/null || echo "")
            if [ -n "$NEW_TOKEN" ]; then
                AUTH_TOKEN="$NEW_TOKEN"
                print_status "OK" "Auth token updated from login"
            fi
        else
            print_status "FAIL" "User login failed with status $status_code"
            echo "Response: $response_body"
        fi
    fi
    
    # Test 4: Protected Endpoints (if we have a token)
    if [ -n "$AUTH_TOKEN" ]; then
        echo -e "\n🔒 Test 4: Protected Endpoints with Authentication"
        
        # Test Pre-scripts listing
        test_endpoint "GET" "/prescripts" "200" "" "-H \"Authorization: Bearer $AUTH_TOKEN\""
        
        # Test Metrics
        test_endpoint "GET" "/metrics" "200" "" "-H \"Authorization: Bearer $AUTH_TOKEN\""
        
        # Test Task execution
        echo -e "\n🤖 Test 5: Task Execution"
        task_data="{\"instruction\":\"Take a screenshot of the homepage\",\"url\":\"https://httpbin.org\",\"options\":{\"timeout\":30000,\"headless\":true}}"
        
        if response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE/execute" \
                      -H "Content-Type: application/json" \
                      -H "Authorization: Bearer $AUTH_TOKEN" \
                      -d "$task_data"); then
            
            status_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
            response_body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
            
            if [ "$status_code" = "202" ] || [ "$status_code" = "200" ]; then
                print_status "OK" "Task execution accepted"
            else
                print_status "FAIL" "Task execution failed with status $status_code"
                echo "Response: $response_body"
            fi
        fi
    else
        echo -e "\n⚠️  Skipping protected endpoint tests - no auth token available"
    fi
    
    # Test 5: Error Handling
    echo -e "\n❌ Test 6: Error Handling"
    
    # Test 404
    test_endpoint "GET" "/non-existent-endpoint" "404"
    
    # Test unauthenticated access to protected endpoint
    test_endpoint "POST" "/execute" "401" "{\"instruction\":\"test\"}"
    
    # Test invalid login
    invalid_login_data="{\"email\":\"<EMAIL>\",\"password\":\"wrongpassword\"}"
    test_endpoint "POST" "/auth/login" "401" "$invalid_login_data"
    
    echo -e "\n🎉 API E2E Tests Completed!"
    echo "================================"
}

# Run tests
main "$@"
