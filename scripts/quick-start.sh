#!/bin/bash

# AERY Quick Start Script
# This script sets up the complete AERY development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
PACKAGE="📦"
DATABASE="🗄️"
GLOBE="🌐"
FIRE="🔥"

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    AERY Quick Start                          ║"
    echo "║              Browser Automation API                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${CYAN}${1}${NC} $2"
}

print_success() {
    echo -e "${GREEN}${CHECK}${NC} $1"
}

print_error() {
    echo -e "${RED}${CROSS}${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}${WARNING}${NC} $1"
}

print_info() {
    echo -e "${BLUE}${INFO}${NC} $1"
}

check_prerequisites() {
    print_step "${GEAR}" "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    print_success "Docker is installed"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    print_success "Docker Compose is installed"
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_success "Docker is running"
    
    # Check for OrbStack (macOS optimization)
    if [[ "$OSTYPE" == "darwin"* ]] && command -v orbctl &> /dev/null; then
        print_success "OrbStack detected - will use optimized configuration"
        USE_ORBSTACK=true
    else
        USE_ORBSTACK=false
    fi
}

setup_environment() {
    print_step "${GEAR}" "Setting up environment..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env from .env.example"
        else
            print_warning ".env.example not found, creating basic .env"
            cat > .env << EOF
# AERY Environment Configuration
NODE_ENV=development
DATABASE_URL=postgresql://aery:aery_dev_password@localhost:5432/aery
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key-change-in-production
ANTHROPIC_API_KEY=your-anthropic-api-key
OPENAI_API_KEY=your-openai-api-key
EOF
            print_success "Created basic .env file"
        fi
    else
        print_success ".env file already exists"
    fi
}

build_images() {
    print_step "${PACKAGE}" "Building Docker images..."
    
    echo "Building gateway image..."
    docker-compose -f docker-compose.dev.yml build gateway
    
    echo "Building workers image..."
    docker-compose -f docker-compose.dev.yml build workers
    
    print_success "All Docker images built successfully"
}

start_services() {
    print_step "${ROCKET}" "Starting services..."
    
    if [ "$USE_ORBSTACK" = true ]; then
        print_info "Using OrbStack optimized configuration"
        docker-compose -f docker-compose.orbstack.yml up -d
        COMPOSE_FILE="docker-compose.orbstack.yml"
    else
        print_info "Using standard Docker configuration"
        docker-compose -f docker-compose.dev.yml up -d
        COMPOSE_FILE="docker-compose.dev.yml"
    fi
    
    print_success "Services started successfully"
}

wait_for_services() {
    print_step "${DATABASE}" "Waiting for services to be ready..."
    
    echo "Waiting for database..."
    sleep 10
    
    echo "Waiting for Redis..."
    sleep 5
    
    echo "Waiting for services to initialize..."
    sleep 15
    
    print_success "Services are ready"
}

show_service_urls() {
    print_step "${GLOBE}" "Service URLs:"
    echo ""
    
    if [ "$USE_ORBSTACK" = true ]; then
        echo -e "${GREEN}🔗 API Gateway:${NC}   http://localhost:8000"
        echo -e "${GREEN}🗄️  PgAdmin:${NC}       http://localhost:8080"
        echo -e "${GREEN}🔴 Redis UI:${NC}      http://localhost:8081"
        echo -e "${GREEN}📧 MailHog:${NC}       http://localhost:8025"
    else
        echo -e "${GREEN}🔗 API Gateway:${NC}   http://localhost:8000"
        echo -e "${GREEN}🗄️  PgAdmin:${NC}       http://localhost:8080"
        echo -e "${GREEN}🔴 Redis UI:${NC}      http://localhost:8081"
        echo -e "${GREEN}📧 MailHog:${NC}       http://localhost:8025"
    fi
    
    echo ""
    print_info "Default PgAdmin credentials: <EMAIL> / admin"
    print_info "Default database: aery / aery / aery_dev_password"
}

test_api() {
    print_step "${FIRE}" "Testing API..."
    
    echo "Testing health endpoint..."
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "API is responding"
    else
        print_warning "API might still be starting up"
        print_info "You can test manually with: curl http://localhost:8000/health"
    fi
}

show_next_steps() {
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                        Next Steps                           ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${YELLOW}Development Commands:${NC}"
    echo "  make dev-logs          # View logs from all services"
    echo "  make health            # Check service health"
    echo "  make test              # Run all tests"
    echo "  make stop              # Stop all services"
    echo ""
    echo -e "${YELLOW}Quick API Test:${NC}"
    echo "  curl http://localhost:8000/health"
    echo ""
    echo -e "${YELLOW}Documentation:${NC}"
    echo "  README.md              # Complete documentation"
    echo "  docs/                  # Additional documentation"
    echo ""
    print_success "AERY development environment is ready!"
}

main() {
    print_header
    
    check_prerequisites
    setup_environment
    build_images
    start_services
    wait_for_services
    show_service_urls
    test_api
    show_next_steps
}

# Run main function
main "$@"
